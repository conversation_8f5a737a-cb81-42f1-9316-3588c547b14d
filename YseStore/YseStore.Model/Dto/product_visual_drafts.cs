using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class product_visual_drafts
    {
        /// <summary>
        /// 
        /// </summary>
        public product_visual_drafts()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 DraftsId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Themes { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Name { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Config { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? AccTime { get; set; }

        /// <summary>
        /// 是否可见
        /// </summary>
        public System.Boolean? Visible { get; set; }


        public System.Int32 ProductId { get; set; }
    }
}