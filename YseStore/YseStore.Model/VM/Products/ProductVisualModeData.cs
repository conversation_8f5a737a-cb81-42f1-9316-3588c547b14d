using System;
using System.Collections.Generic;
using System.IO.Pipelines;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static System.Reflection.Metadata.BlobBuilder;

namespace YseStore.Model.VM.Products
{
    public static class ProductVisualModeData
    {
        public static Dictionary<string, Object> DiscountTypeDic { get; set; } = new Dictionary<string, Object>
        {
            { "productkv", new { Mode="mode_1",  Settings=new { } ,Blocks=new string[] {"PicPc" } } },
            { "poster", "满减优惠" },
            { "recall_discount", "召回折扣" }
        };
    }
}
