using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Common.Cache;
using YseStore.Repo;

namespace YseStore.Service
{
    public class PhotoService : BaseServices<photo>, IPhotoService
    {
        private readonly IStringLocalizer<UrlService> _localizer;

        private readonly ILogger<UrlService> _logger;
        private readonly ICaching _cacheService;

        /// <summary>
        /// 构造函数，通过依赖注入获取数据仓储
        /// </summary>
        /// <param name="baseDal">UrlService</param>
        public PhotoService(IBaseRepository<photo> baseDal, ILogger<UrlService> logger, ICaching cacheService, IStringLocalizer<UrlService> localizer) : base(baseDal)
        {
            _logger = logger;
            this._cacheService = cacheService;
            _localizer = localizer;
        }
    }
}
