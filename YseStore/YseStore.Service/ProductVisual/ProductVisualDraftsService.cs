using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Common.Cache;
using YseStore.IService.ProductVisual;
using YseStore.IService.Visual;

namespace YseStore.Service.ProductVisual
{
    /// <summary>
    /// 店铺装修草稿
    /// </summary>
    public class ProductVisualDraftsService : BaseServices<product_visual_drafts>, IProductVisualDraftsService
    {
        private readonly ISqlSugarClient db;
        private readonly ICaching _caching;
        private readonly ILogger<ProductVisualDraftsService> _logger;

        public ProductVisualDraftsService(ISqlSugarClient db, ICaching caching, ILogger<ProductVisualDraftsService> logger)
        {
            this.db = db;
            _caching = caching;
            _logger = logger;
        }

       
    }
}
