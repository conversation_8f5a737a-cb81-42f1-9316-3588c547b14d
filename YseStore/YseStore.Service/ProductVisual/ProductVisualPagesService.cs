using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Common.Cache;
using YseStore.IService.ProductVisual;
using YseStore.IService.Visual;

namespace YseStore.Service.ProductVisual
{
    /// <summary>
    /// 店铺装修页面
    /// </summary>
    public class ProductVisualPagesService : BaseServices<product_visual_pages>, IProductVisualPagesService
    {
        private readonly ISqlSugarClient db;
        private readonly ICaching _caching;
        private readonly ILogger<product_visual_pages> _logger;
        public ProductVisualPagesService(ISqlSugarClient db, ICaching caching, ILogger<product_visual_pages> logger)
        {
            this.db = db;
            _caching = caching;
            _logger = logger;
        }
        
    }
}
