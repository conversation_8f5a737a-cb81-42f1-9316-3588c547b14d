using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Common;
using YseStore.Common.Cache;
using YseStore.IService.ProductVisual;
using YseStore.IService.Visual;

namespace YseStore.Service.ProductVisual
{
    /// <summary>
    /// 店铺装修插件
    /// </summary>
    public class ProductVisualPluginsService : BaseServices<product_visual_plugins>, IProductVisualPluginsService
    {
        private readonly ISqlSugarClient db;
        private readonly ICaching _caching;
        private readonly ILogger<ProductVisualPluginsService> _logger;
        private readonly IStringLocalizer<ProductVisualPluginsService> T;

        public ProductVisualPluginsService(ISqlSugarClient db, ICaching caching, ILogger<ProductVisualPluginsService> logger, IStringLocalizer<ProductVisualPluginsService> localizer)
        {
            this.db = db;
            _caching = caching;
            _logger = logger;
            T = localizer;
        }



    }
}
